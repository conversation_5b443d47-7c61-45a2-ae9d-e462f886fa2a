"use client";

import { useState, Suspense } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
// Select components are not used in this file as we're using native select
import { Textarea } from "@/components/ui/textarea";
import { Mail, Phone } from "lucide-react";
import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import ClientOnly from "@/components/ClientOnly";
import Cal, { getCalApi } from "@calcom/embed-react";
import { useTranslations, useLocale } from 'next-intl';

declare global {
  interface Window {
    Calendly?: {
      initInlineWidget: (options: {
        url: string;
        parentElement: HTMLElement | null;
        prefill?: Record<string, unknown>;
        utm?: Record<string, unknown>;
      }) => void;
    }
  }
}

// Wrapper component that uses searchParams
function ContactForm() {
  const searchParams = useSearchParams();
  const t = useTranslations('ContactPage');
  const locale = useLocale();

  const [formData, setFormData] = useState<{
    name: string;
    email: string;
    services: string[];
    message: string;
  }>({
    name: '',
    email: '',
    services: [],
    message: ''
  });

  // State to track which category is expanded
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);

  // Handle URL query parameters on page load
  useEffect(() => {
    const serviceParam = searchParams ? searchParams.get('service') : null;
    if (serviceParam) {
      // If the service is a chatbot option, expand the chatbot category
      if (serviceParam === 'chatbot' || serviceParam.startsWith('chatbot-')) {
        setExpandedCategory('chatbot');

        // If it's a specific chatbot service, select it
        if (serviceParam === 'chatbot-mvp' || serviceParam === 'chatbot-custom') {
          setFormData(prev => ({ ...prev, services: [serviceParam] }));
        }
      }
      // If the service is a web development option, expand the webdev category
      else if (serviceParam === 'webdev' || serviceParam.startsWith('webdev-')) {
        setExpandedCategory('webdev');

        // If it's a specific web development service, select it
        if (['webdev-essential', 'webdev-business', 'webdev-advanced'].includes(serviceParam)) {
          setFormData(prev => ({ ...prev, services: [serviceParam] }));
        }
      }
    }
  }, [searchParams]);

  // Function to handle category selection
  const handleCategorySelect = (category: string) => {
    if (expandedCategory === category) {
      // If clicking the same category, collapse it
      setExpandedCategory(null);
    } else {
      // Expand the clicked category
      setExpandedCategory(category);
    }

    // Clear error for service field when category changes
    if (formErrors.service) {
      setFormErrors(prevErrors => {
        const newErrors = { ...prevErrors };
        delete newErrors.service;
        return newErrors;
      });
    }
  };

  // Function to handle service option selection (supports multiple, but only one per category)
  const handleServiceSelect = (service: string) => {
    setFormData(prev => {
      const isSelected = prev.services.includes(service);
      let newServices = [...prev.services];

      if (isSelected) {
        // Remove if already selected
        newServices = newServices.filter(s => s !== service);
      } else {
        // Add the new service, but first remove any other service from the same category
        if (service.startsWith('chatbot-')) {
          // Remove any existing chatbot services
          newServices = newServices.filter(s => !s.startsWith('chatbot-'));
        } else if (service.startsWith('webdev-')) {
          // Remove any existing webdev services
          newServices = newServices.filter(s => !s.startsWith('webdev-'));
        }
        // Add the new service
        newServices.push(service);
      }

      return { ...prev, services: newServices };
    });

    // Clear error for service field when value changes
    if (formErrors.service) {
      setFormErrors(prevErrors => {
        const newErrors = { ...prevErrors };
        delete newErrors.service;
        return newErrors;
      });
    }
  };

  // Function to remove a selected service
  const removeService = (serviceToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      services: prev.services.filter(service => service !== serviceToRemove)
    }));

    // If removing 'other' service, also clear the expanded category
    if (serviceToRemove === 'other') {
      setExpandedCategory(null);
    }
  };

  // Add CSS animation for the interactive service selection
  useEffect(() => {
    // Add the animation CSS if it doesn't exist
    if (!document.getElementById('service-animation-styles')) {
      const styleEl = document.createElement('style');
      styleEl.id = 'service-animation-styles';
      styleEl.textContent = `
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out forwards;
        }
      `;
      document.head.appendChild(styleEl);
    }

    return () => {
      // Clean up the style element when component unmounts
      const styleEl = document.getElementById('service-animation-styles');
      if (styleEl) styleEl.remove();
    };
  }, []);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState(''); // For general API errors
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [formErrors, setFormErrors] = useState<{
    name?: string;
    email?: string;
    service?: string;
    message?: string;
  }>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormErrors({}); // Clear previous errors
    setSubmitError('');
    setSubmitSuccess(false);

    // --- Validation ---
    const errors: typeof formErrors = {};
    if (!formData.name.trim()) errors.name = t('nameRequired');
    if (!formData.email.trim()) {
      errors.email = t('emailRequired');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = t('emailInvalid');
    }
    if (formData.services.length === 0) errors.service = t('serviceRequired');
    if (!formData.message.trim()) errors.message = t('messageRequired');

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return; // Stop submission if validation fails
    }
    // --- End Validation ---

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          service: formData.services.join(', '), // Convert array to string for backward compatibility
          locale: locale
        }),
      });

      if (!response.ok) {
        let errorMessage = 'Failed to submit form';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch {
          // If response is not JSON, use default message
        }
        throw new Error(errorMessage);
      }

      await response.json(); // Consume the response
      setSubmitSuccess(true);
      setFormData({
        name: '',
        email: '',
        services: [],
        message: ''
      });
    } catch (error: unknown) {
      console.error('Contact form submission error:', error);
      setSubmitError(error instanceof Error ? error.message : 'Something went wrong. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear the error for the field being changed
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors(prevErrors => {
        const newErrors = { ...prevErrors };
        delete newErrors[name as keyof typeof formErrors];
        return newErrors;
      });
    }
  };

  // We're using the native select element with the handleChange function

  // Cal.com embed initialization with proper CSS variables and error handling
  useEffect(() => {
    (async function () {
      try {
        const cal = await getCalApi({"namespace":"30min"});
        cal("ui", {
          "theme": "light",
          "styles": {
            "branding": {
              "brandColor": "#7c3aed"
            }
          },
          "hideEventTypeDetails": false,
          "layout": "month_view"
        });
      } catch (error) {
        console.error('Cal.com initialization error:', error);
      }
    })();

    // Hide Cal.com branding and add container styling
    const style = document.createElement('style');
    style.textContent = `
      /* Hide branding */
      [data-cal-namespace="30min"] [data-testid="powered-by-cal"] {
        display: none !important;
      }
      [data-cal-namespace="30min"] .cal-branding {
        display: none !important;
      }
      [data-cal-namespace="30min"] a[href*="cal.com"] {
        display: none !important;
      }

      /* Add grayish overlay to hide any remaining branding */
      [data-cal-namespace="30min"] {
        position: relative !important;
      }

      [data-cal-namespace="30min"]::after {
        content: '' !important;
        position: absolute !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        height: 40px !important;
        background: linear-gradient(to top, rgba(107, 114, 128, 0.9) 0%, rgba(107, 114, 128, 0.7) 50%, transparent 100%) !important;
        pointer-events: none !important;
        z-index: 10 !important;
      }

      /* Alternative: solid gray background for bottom area */
      [data-cal-namespace="30min"] > div:last-child {
        background: #6b7280 !important;
        padding-bottom: 20px !important;
      }
    `;
    document.head.appendChild(style);
  }, []);

  // ORIGINAL CALENDLY CODE - COMMENTED OUT
  /*
  useEffect(() => {
  //   const initCalendly = () => {
  //     if (isInitializedRef.current) {
  //       console.log('⚠️ Calendly already initialized, skipping...');
  //       return;
  //     }

  //     const widgetElement = document.querySelector('.calendly-inline-widget');
  //     if (!widgetElement) {
  //       console.warn('⚠️ Calendly widget element not found');
  //       return;
  //     }

  //     // Clear any existing content to prevent duplicates
  //     widgetElement.innerHTML = `
  //       <div class="flex flex-col items-center justify-center h-full w-full text-center p-8">
  //         <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mb-4"></div>
  //         <p class="text-purple-100/70 mb-4">Loading calendar...</p>
  //       </div>
  //     `;

  //     if (!window.Calendly) {
  //       console.warn('⚠️ Calendly script not loaded yet, retrying...');
  //       setTimeout(initCalendly, 500);
  //       return;
  //     }

  //     try {
  //       console.log('🔄 Attempting to initialize Calendly inline widget...');
  //       console.log('📍 Widget element:', widgetElement);
  //       console.log('🌐 Calendly object:', window.Calendly);

  //       // Clear the loading message
  //       widgetElement.innerHTML = '';

  //       window.Calendly.initInlineWidget({
  //         url: 'https://calendly.com/pilybas-edgaras/30min',
  //         parentElement: widgetElement as HTMLElement,
  //         prefill: {},
  //         utm: {}
  //       });
  //       isInitializedRef.current = true;
  //       console.log('✅ Calendly inline widget initialized successfully');
  //     } catch (error) {
  //       console.error('❌ Calendly initialization failed:', error);
  //       // Show error message to user
  //       widgetElement.innerHTML = `
  //         <div class="flex flex-col items-center justify-center h-full w-full text-center p-8">
  //           <p class="text-red-400 mb-4">Failed to load calendar</p>
  //           <p class="text-purple-100/70 text-sm">Please try refreshing the page</p>
  //         </div>
  //       `;
  //     }
  //   };

  //   // Check if Calendly script is already loaded
  //   if (window.Calendly) {
  //     console.log('✅ Calendly already loaded, initializing...');
  //     initCalendly();
  //     return;
  //   }

  //   // Check if script is already in DOM
  //   const existingScript = document.querySelector('script[src*="calendly.com"]');
  //   if (existingScript) {
  //     console.log('⏳ Calendly script already in DOM, waiting for load...');
  //     existingScript.addEventListener('load', initCalendly);
  //     return;
  //   }

  //   // Load Calendly script only once
  //   console.log('📥 Loading Calendly script...');
  //   const script = document.createElement('script');
  //   script.src = 'https://assets.calendly.com/assets/external/widget.js';
  //   script.async = true;
  //   script.id = 'calendly-script'; // Add ID to prevent duplicates

  //   script.onload = () => {
  //     console.log('✅ Calendly script loaded successfully');
  //     setTimeout(initCalendly, 100);
  //   };

  //   script.onerror = (error) => {
  //     console.error('❌ Failed to load Calendly script:', error);
  //     // Show fallback message
  //     const widgetElement = document.querySelector('.calendly-inline-widget');
  //     if (widgetElement) {
  //       widgetElement.innerHTML = `
  //         <div class="flex flex-col items-center justify-center h-full w-full text-center p-8">
  //           <p class="text-red-400 mb-4">Unable to load calendar</p>
  //           <p class="text-purple-100/70 text-sm mb-4">Please book directly:</p>
  //           <a href="https://calendly.com/pilybas-edgaras/30min" target="_blank" rel="noopener noreferrer"
  //              class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors">
  //             Open Calendar in New Tab
  //           </a>
  //         </div>
  //       `;
  //     }
  //   };

  //   document.head.appendChild(script);

  //   return () => {
  //     // Cleanup - but don't reset isInitializedRef as it should persist
  //     // The widget will be cleaned up when the component unmounts
  //   };
  // }, []); // Empty dependency array to run only once
  */

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-950 via-purple-900 to-purple-950 pt-32 pb-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Left Column */}
          <div className="space-y-8">
            <div>
              <h1 className="text-5xl font-bold text-white mb-4">
                {t('heroTitle')}
              </h1>
              <p className="text-xl text-purple-100/70">
                {t('heroSubtitle')}
              </p>
            </div>

            {/* Contact Info Blocks */}
            <div className="space-y-6">
              {/* Email Block */}
              <div className="bg-purple-900/30 backdrop-blur-sm rounded-lg p-6 border border-purple-700/20 flex items-center gap-4">
                <div className="bg-gradient-to-br from-pink-500 to-purple-500 p-3 rounded-lg flex-shrink-0">
                  <Mail className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">{t('email')}</h3>
                  <p className="text-purple-100/70"><EMAIL></p>
                </div>
              </div>
              {/* Phone Block */}
              <div className="bg-purple-900/30 backdrop-blur-sm rounded-lg p-6 border border-purple-700/20 flex items-center gap-4">
                <div className="bg-gradient-to-br from-pink-500 to-purple-500 p-3 rounded-lg flex-shrink-0">
                  <Phone className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">{t('phone')}</h3>
                  <p className="text-purple-100/70">+31 610 271 038</p>
                </div>
              </div>
              {/* Address Block */}
              {/* <div className="bg-purple-900/30 backdrop-blur-sm rounded-lg p-6 border border-purple-700/20 flex items-center gap-4">
                <div className="bg-gradient-to-br from-pink-500 to-purple-500 p-3 rounded-lg flex-shrink-0">
                  <MapPin className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">{t('address')}</h3>
                  <p className="text-purple-100/70">Laplace-gebouw, Laplace 32, 5612 AJ Eindhoven</p>
                </div>
              </div> */}
            </div>
          </div>

          {/* Right Column (Form) */}
          <ClientOnly fallback={
            <div id="contact-form" className="bg-white rounded-2xl p-8 shadow-xl">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">Contact us</h2>
              <div className="text-center py-8">
                <p className="text-gray-500">Loading contact form...</p>
              </div>
            </div>
          }>
            <div id="contact-form" className="bg-white rounded-2xl p-8 shadow-xl">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">{t('contactUs')}</h2>
              <form className="space-y-6" onSubmit={handleSubmit}>
              <div> {/* Changed from space-y-2 to allow fixed height error */}
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">{t('name')}</label> {/* Added block and mb-1 */}
                <Input
                  id="name"
                  name="name"
                  className="border-gray-300 focus:border-purple-500 focus:ring-purple-500 transition-colors"
                  placeholder={t('namePlaceholder')}
                  value={formData.name}
                  onChange={handleChange}
                />
                <div className="h-5 mt-1"> {/* Fixed height container for error */}
                  {formErrors.name && <p className="text-red-500 text-xs">{formErrors.name}</p>}
                </div>
              </div>
              <div> {/* Changed from space-y-2 */}
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">{t('email')}</label> {/* Added block and mb-1 */}
                <Input
                  id="email"
                  name="email"
                  className="border-gray-300 focus:border-purple-500 focus:ring-purple-500 transition-colors"
                  type="email"
                  placeholder={t('emailPlaceholder')}
                  value={formData.email}
                  onChange={handleChange}
                />
                <div className="h-5 mt-1"> {/* Fixed height container for error */}
                  {formErrors.email && <p className="text-red-500 text-xs">{formErrors.email}</p>}
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <label className="block text-sm font-medium text-gray-700">{t('service')}</label>
                
                </div>

                {/* Interactive Service Selection */}
                <div className="space-y-4">
                  {/* Main Service Categories */}
                  <div className="grid grid-cols-2 gap-3 relative items-stretch">
                    {/* Decorative elements */}
                    <div className="absolute -top-6 -right-6 w-12 h-12 bg-purple-500/5 rounded-full blur-xl"></div>
                    <div className="absolute -bottom-6 -left-6 w-12 h-12 bg-blue-500/5 rounded-full blur-xl"></div>
                    {/* Chatbot Integration Category */}
                    <div
                      className={`relative overflow-hidden rounded-lg cursor-pointer transition-all duration-300 transform hover:scale-[1.02] min-h-[100px] flex ${expandedCategory === 'chatbot' ? 'ring-4 ring-purple-500 shadow-xl scale-[1.03]' : 'hover:shadow-md'}`}
                      onClick={() => handleCategorySelect('chatbot')}
                    >
                      <div className="bg-gradient-to-br from-purple-600 to-pink-500 p-4 text-white relative overflow-hidden w-full flex flex-col justify-between">
                        {/* Decorative elements */}
                        <div className="absolute -top-6 -right-6 w-12 h-12 bg-white/10 rounded-full"></div>
                        <div className="absolute -bottom-6 -left-6 w-8 h-8 bg-white/5 rounded-full"></div>
                        <div className="flex items-center justify-between flex-shrink-0">
                          <h3 className="font-semibold">{t('chatbotIntegration')}</h3>
                          <div className="bg-white/20 rounded-full p-1">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M8 10L12 14L16 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          </div>
                        </div>
                        <p className="text-xs mt-1 text-white/80 flex-grow flex items-end">{t('chatbotIntegrationDesc')}</p>
                      </div>

                      {/* Subtle gradient overlay */}
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-600/10 to-pink-500/10 pointer-events-none"></div>
                    </div>

                    {/* Web Development Category */}
                    <div
                      className={`relative overflow-hidden rounded-lg cursor-pointer transition-all duration-300 transform hover:scale-[1.02] min-h-[100px] flex ${expandedCategory === 'webdev' ? 'ring-4 ring-blue-500 shadow-xl scale-[1.03]' : 'hover:shadow-md'}`}
                      onClick={() => handleCategorySelect('webdev')}
                    >
                      <div className="bg-gradient-to-br from-blue-600 to-cyan-500 p-4 text-white relative overflow-hidden w-full flex flex-col justify-between">
                        {/* Decorative elements */}
                        <div className="absolute -top-6 -right-6 w-12 h-12 bg-white/10 rounded-full"></div>
                        <div className="absolute -bottom-6 -left-6 w-8 h-8 bg-white/5 rounded-full"></div>
                        <div className="flex items-center justify-between flex-shrink-0">
                          <h3 className="font-semibold">{t('webDevelopment')}</h3>
                          <div className="bg-white/20 rounded-full p-1">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M8 10L12 14L16 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          </div>
                        </div>
                        <p className="text-xs mt-1 text-white/80 flex-grow flex items-end">{t('webDevelopmentDesc')}</p>
                      </div>

                      {/* Subtle gradient overlay */}
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 to-cyan-500/10 pointer-events-none"></div>
                    </div>

                    {/* Other Services Option */}
                    <div
                      className={`col-span-2 relative overflow-hidden rounded-lg cursor-pointer transition-all duration-300 transform hover:scale-[1.02] ${expandedCategory === 'other' || formData.services.includes('other') ? 'ring-4 ring-gray-500 shadow-xl scale-[1.03]' : 'hover:shadow-md'}`}
                      onClick={() => {
                        handleCategorySelect('other');
                        handleServiceSelect('other');
                      }}
                    >
                      <div className="bg-gradient-to-br from-gray-700 to-gray-600 p-4 text-white relative overflow-hidden">
                        {/* Decorative elements */}
                        <div className="absolute -top-6 -right-6 w-12 h-12 bg-white/10 rounded-full"></div>
                        <div className="absolute -bottom-6 -left-6 w-8 h-8 bg-white/5 rounded-full"></div>
                        <div className="flex items-center justify-between">
                          <h3 className="font-semibold">{t('otherServices')}</h3>
                          <div className="bg-white/20 rounded-full p-1">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M12 5V19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          </div>
                        </div>
                        <p className="text-xs mt-1 text-white/80">{t('otherServicesDesc')}</p>
                      </div>

                      {/* Subtle gradient overlay */}
                      <div className="absolute inset-0 bg-gradient-to-br from-gray-700/10 to-gray-600/10 pointer-events-none"></div>
                    </div>
                  </div>

                  {/* Chatbot Sub-options */}
                  {expandedCategory === 'chatbot' && (
                    <div className="pl-4 border-l-4 border-purple-500 space-y-3 animate-fadeIn mt-3">
                      <div
                        className={`bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md ${formData.services.includes('chatbot-mvp') ? 'ring-3 ring-purple-500 shadow-md scale-[1.02]' : ''}`}
                        onClick={() => handleServiceSelect('chatbot-mvp')}
                      >
                        <div className="flex items-center">
                          <div className={`w-5 h-5 rounded-full mr-2 ${formData.services.includes('chatbot-mvp') ? 'bg-purple-500 ring-2 ring-white' : 'border-2 border-purple-500/50'}`}></div>
                          <div>
                            <h4 className="font-medium text-gray-800">{t('mvpVirtualAssistant')}</h4>
                            <p className="text-xs text-gray-500">{t('mvpVirtualAssistantDesc')}</p>
                          </div>
                        </div>
                      </div>

                      <div
                        className={`bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md ${formData.services.includes('chatbot-custom') ? 'ring-3 ring-purple-500 shadow-md scale-[1.02]' : ''}`}
                        onClick={() => handleServiceSelect('chatbot-custom')}
                      >
                        <div className="flex items-center">
                          <div className={`w-5 h-5 rounded-full mr-2 ${formData.services.includes('chatbot-custom') ? 'bg-purple-500 ring-2 ring-white' : 'border-2 border-purple-500/50'}`}></div>
                          <div>
                            <h4 className="font-medium text-gray-800">{t('customizableAiAssistant')}</h4>
                            <p className="text-xs text-gray-500">{t('customizableAiAssistantDesc')}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Web Development Sub-options */}
                  {expandedCategory === 'webdev' && (
                    <div className="pl-4 border-l-4 border-blue-500 space-y-3 animate-fadeIn mt-3">
                      <div
                        className={`bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md ${formData.services.includes('webdev-essential') ? 'ring-3 ring-blue-500 shadow-md scale-[1.02]' : ''}`}
                        onClick={() => handleServiceSelect('webdev-essential')}
                      >
                        <div className="flex items-center">
                          <div className={`w-5 h-5 rounded-full mr-2 ${formData.services.includes('webdev-essential') ? 'bg-blue-500 ring-2 ring-white' : 'border-2 border-blue-500/50'}`}></div>
                          <div>
                            <h4 className="font-medium text-gray-800">{t('websiteEssentials')}</h4>
                            <p className="text-xs text-gray-500">{t('websiteEssentialsDesc')}</p>
                          </div>
                        </div>
                      </div>

                      <div
                        className={`bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md ${formData.services.includes('webdev-business') ? 'ring-3 ring-blue-500 shadow-md scale-[1.02]' : ''}`}
                        onClick={() => handleServiceSelect('webdev-business')}
                      >
                        <div className="flex items-center">
                          <div className={`w-5 h-5 rounded-full mr-2 ${formData.services.includes('webdev-business') ? 'bg-blue-500 ring-2 ring-white' : 'border-2 border-blue-500/50'}`}></div>
                          <div>
                            <h4 className="font-medium text-gray-800">{t('smartBusinessWebsites')}</h4>
                            <p className="text-xs text-gray-500">{t('smartBusinessWebsitesDesc')}</p>
                          </div>
                        </div>
                      </div>

                      <div
                        className={`bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md ${formData.services.includes('webdev-advanced') ? 'ring-3 ring-blue-500 shadow-md scale-[1.02]' : ''}`}
                        onClick={() => handleServiceSelect('webdev-advanced')}
                      >
                        <div className="flex items-center">
                          <div className={`w-5 h-5 rounded-full mr-2 ${formData.services.includes('webdev-advanced') ? 'bg-blue-500 ring-2 ring-white' : 'border-2 border-blue-500/50'}`}></div>
                          <div>
                            <h4 className="font-medium text-gray-800">{t('advancedWebPlatforms')}</h4>
                            <p className="text-xs text-gray-500">{t('advancedWebPlatformsDesc')}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="h-5"> {/* Fixed height container for error */}
                  {formErrors.service && <p className="text-red-500 text-xs">{formErrors.service}</p>}
                </div>
              </div>

              {/* Selected Services Pills */}
              {formData.services.length > 0 && (
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">{t('selectedServices')}</label>
                  <div className="flex flex-wrap gap-2">
                    {formData.services.map((service) => {
                      const serviceLabels: { [key: string]: string } = {
                        'chatbot-mvp': t('mvpVirtualAssistant'),
                        'chatbot-custom': t('customizableAiAssistant'),
                        'webdev-essential': t('websiteEssentials'),
                        'webdev-business': t('smartBusinessWebsites'),
                        'webdev-advanced': t('advancedWebPlatforms'),
                        'other': t('otherServices')
                      };

                      return (
                        <div
                          key={service}
                          className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white px-3 py-1.5 rounded-full text-sm font-medium shadow-md hover:shadow-lg transition-all duration-200"
                        >
                          <span>{serviceLabels[service] || service}</span>
                          <button
                            type="button"
                            onClick={() => removeService(service)}
                            className="ml-1 hover:bg-white/20 rounded-full p-0.5 transition-colors duration-200"
                            aria-label={`Remove ${serviceLabels[service] || service}`}
                          >
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M18 6L6 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          </button>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              <div> {/* Changed from space-y-2 */}
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">{t('message')}</label>
                <Textarea
                  id="message"
                  name="message"
                  className="border-gray-300 focus:border-purple-500 focus:ring-purple-500 min-h-[150px] transition-colors"
                  placeholder={t('messagePlaceholder')}
                  value={formData.message}
                  onChange={handleChange}
                />
                <div className="h-5 mt-1"> {/* Fixed height container for error */}
                  {formErrors.message && <p className="text-red-500 text-xs">{formErrors.message}</p>}
                </div>
              </div>
              {submitError && (
                <p className="text-red-500 text-sm">{submitError}</p>
              )}
              {submitSuccess && (
                <div className="flex flex-col items-center justify-center space-y-2 animate-fade-in">
                  <svg
                    className="w-20 h-20 text-green-500 animate-outgoing-pop"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <polyline points="20 6 9 17 4 12" />
                  </svg>
                  <p className="text-green-500 text-sm transition-opacity duration-500 ease-out">
                    {t('thankYou')}
                  </p>
                </div>
              )}
              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-lg shadow-purple-500/20 transition-all duration-200 hover:scale-105"
                disabled={isSubmitting}
              >
                {isSubmitting ? t('sending') : t('contact')}
              </Button>
            </form>
          </div>
          </ClientOnly>
        </div>

        {/* Divider with "Or" */}
        <div className="relative my-20">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t-2 border-purple-500/50"></div>
          </div>
          <div className="relative flex justify-center">
            <span className="bg-gradient-to-r from-pink-600 to-purple-600 px-6 py-2 text-3xl font-bold text-white rounded-full shadow-lg">
              {t('orTitle')}
            </span>
          </div>
        </div>

        {/* Calendly Scheduling Section */}
        <ClientOnly fallback={
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-20">
            <div className="text-center mb-10">
              <h2 className="text-3xl font-bold text-white mb-4">{t('scheduleMeeting')}</h2>
              <p className="text-xl text-purple-100/70">
                Loading calendar...
              </p>
            </div>
          </div>
        }>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-20">
            <div className="text-center mb-10">
              <h2 className="text-3xl font-bold text-white mb-4">{t('scheduleMeeting')}</h2>
              <p className="text-xl text-purple-100/70">
                {t('scheduleMeetingDesc')}
              </p>
            </div>
            <div className="flex justify-center">
              <div className="bg-purple-950/80 backdrop-blur-sm rounded-2xl p-0 border border-purple-700/20 w-full max-w-5xl mx-auto shadow-2xl relative">
                <div className="rounded-2xl overflow-hidden bg-white relative" style={{ minWidth: "320px", minHeight: "600px", height: "auto", width: "100%" }}>
                  <Cal
                    namespace="30min"
                    calOrigin="https://cal.com"
                    calLink="bailey/15min"
                    style={{width:"100%",minHeight:"600px",height:"auto",overflow:"scroll"}}
                    config={{
                      "layout": "month_view",
                      "theme": "light"
                    }}
                    onError={(error) => {
                      console.error('Cal.com embed error:', error);
                    }}
                  />
                  
                  {/* Overlay to hide Cal.com branding */}
                  <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-white via-white to-transparent pointer-events-none z-10"></div>
                  
                  {/* Custom footer to replace Cal.com branding */}
                  <div className="absolute bottom-0 left-0 right-0 h-28 bg-white flex items-center justify-center border-t border-gray-100 z-20">
                   
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ClientOnly>
      </div>
    </div>
  );
}

// Main page component that wraps ContactForm in Suspense
export default function ContactPage() {
  return (
    <Suspense fallback={<div className="min-h-screen bg-gradient-to-br from-purple-950 via-purple-900 to-purple-950 pt-32 pb-20 flex items-center justify-center">
      <div className="text-white text-xl">Loading...</div>
    </div>}>
      <ContactForm />
    </Suspense>
  );
}
